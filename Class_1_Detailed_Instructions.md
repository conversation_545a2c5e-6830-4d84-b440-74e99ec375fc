# Class 1 Student Handout: Excel Fundamentals & Business Functions

## Your Guide to Mastering Essential Excel Skills

---

## 📋 **Quick Reference: Formulas You'll Learn Today**

| Formula               | Syntax                                                    | Purpose                       | Example                                 |
| --------------------- | --------------------------------------------------------- | ----------------------------- | --------------------------------------- |
| **SUM**         | `=SUM(range)`                                           | Adds numbers in a range       | `=SUM(A1:A5)`                         |
| **AVERAGE**     | `=AVERAGE(range)`                                       | Calculates average of numbers | `=AVERAGE(B1:B10)`                    |
| **COUNT**       | `=COUNT(range)`                                         | Counts cells with numbers     | `=COUNT(C1:C20)`                      |
| **MAX**         | `=MAX(range)`                                           | Finds highest value           | `=MAX(D1:D8)`                         |
| **MIN**         | `=MIN(range)`                                           | Finds lowest value            | `=MIN(D1:D8)`                         |
| **VLOOKUP**     | `=VLOOKUP(lookup_value, table_array, col_index, FALSE)` | Finds data in a table         | `=VLOOKUP(101, A1:D10, 2, FALSE)`     |
| **IF**          | `=IF(condition, true_value, false_value)`               | Makes logical decisions       | `=IF(A1>100, "High", "Low")`          |
| **IFERROR**     | `=IFERROR(formula, error_value)`                        | Handles formula errors        | `=IFERROR(VLOOKUP(...), "Not Found")` |
| **CONCATENATE** | `=A1&" "&B1`                                            | Combines text                 | `=A1&" "&B1` (combines with space)    |
| **TODAY**       | `=TODAY()`                                              | Current date                  | `=TODAY()`                            |
| **YEAR**        | `=YEAR(date)`                                           | Extracts year from date       | `=YEAR(A1)`                           |

---

## 📚 **Key Terms & Concepts**

| Term                     | Definition                                         | Example                                | Why It Matters                |
| ------------------------ | -------------------------------------------------- | -------------------------------------- | ----------------------------- |
| **Cell Reference** | Address of a cell using column letter + row number | A1, B5, C10                            | Foundation for all formulas   |
| **Range**          | Group of cells                                     | A1:A10 (cells A1 through A10)          | Used in functions like SUM    |
| **Formula**        | Calculation that starts with =                     | `=A1+B1`                             | Automates calculations        |
| **Function**       | Pre-built formula for common tasks                 | SUM, AVERAGE, VLOOKUP                  | Saves time and reduces errors |
| **Lookup Value**   | What you're searching for in VLOOKUP               | Employee ID: 1001                      | The "question" you're asking  |
| **Table Array**    | Data range where you search                        | A1:D100                                | The "database" you search in  |
| **Column Index**   | Which column to return data from                   | 2 = second column                      | The "answer" location         |
| **Exact Match**    | FALSE in VLOOKUP - finds exact match only          | FALSE                                  | Prevents wrong results        |
| **Nested Formula** | Formula inside another formula                     | `=IF(SUM(A1:A5)>100, "High", "Low")` | Combines multiple functions   |
| **Error Handling** | Managing formula errors gracefully                 | Using IFERROR                          | Professional error management |

---

# Part A: Excel Foundation - Understanding Spreadsheets

## 1. What is a Spreadsheet?

### The Story of Spreadsheets

Before computers, accountants and business people used large paper sheets with rows and columns to organize data. These were called 'spreadsheets' because they literally spread across large sheets of paper. Imagine trying to calculate company budgets by hand with a calculator!

### Evolution Timeline:

- **1979**: VisiCalc - First computer spreadsheet
- **1983**: Lotus 1-2-3 - Dominated business world
- **1987**: Microsoft Excel - Became the standard
- **Today**: Excel is used by 750+ million people worldwide

### What Makes Excel Powerful?

1. **Grid Structure**: Rows (numbers) and Columns (letters) create addresses
2. **Formulas**: Automatic calculations that update when data changes
3. **Functions**: Pre-built formulas for common tasks
4. **Data Analysis**: Sort, filter, and analyze large amounts of data

### The Excel Interface Deep Dive

```
    A    B    C    D    E
1 |    |    |    |    |    |
2 |    |    |    |    |    |
3 |    |    |    |    |    |
4 |    |    |    |    |    |
```

**Cell Address System:**

- Each cell has a unique address (like a street address)
- Column letter + Row number = Cell address
- A1 = Column A, Row 1
- C5 = Column C, Row 5

**Try This Now:**

1. Open Excel and look at the grid
2. Click on cell A1 - notice how it's highlighted
3. Click on cell C5 - see the address in the Name Box
4. This addressing system is the foundation of everything we'll do

---

## 2. Basic Formulas - Your First Calculations

### The Formula Foundation

**Key Concept**: Every formula in Excel starts with an equals sign (=)

**Why the equals sign?**

- Tells Excel: "This is a calculation, not just text"
- Without =, Excel treats it as regular text
- Example: Type "2+2" → Excel shows "2+2"
- Example: Type "=2+2" → Excel shows "4"

### Essential Formula Types

#### A. Basic Math Operations

```excel
=5+3          → 8
=10-4         → 6
=6*7          → 42
=20/5         → 4
=2^3          → 8 (2 to the power of 3)
```

#### B. Cell References in Formulas

```excel
If A1 contains 10 and B1 contains 5:
=A1+B1        → 15
=A1*B1        → 50
=A1-B1        → 5
```

#### C. Essential Functions

```excel
=SUM(A1:A5)      → Adds cells A1 through A5
=AVERAGE(B1:B10) → Average of cells B1 through B10
=COUNT(C1:C20)   → Counts numbers in cells C1 through C20
=MAX(D1:D8)      → Finds highest value in D1 through D8
=MIN(D1:D8)      → Finds lowest value in D1 through D8
```

**Practice Exercise:**

1. Create this simple table:

   ```
   A1: 10    B1: Sales
   A2: 20    B2: Marketing
   A3: 30    B3: HR
   A4: =SUM(A1:A3)
   ```
2. Notice how changing A1 to 15 automatically updates A4 to 65

---

## 3. Essential Formatting for Professional Results

### Why Formatting Matters in Business

In business, presentation is almost as important as the data itself. A well-formatted spreadsheet shows professionalism and makes data easier to understand.

### Key Formatting Categories

#### A. Number Formatting

```excel
Raw Number: 1000
Currency: $1,000.00
Percentage: 10% (if the number was 0.1)
Date: 01/01/2025 (if the number represents a date)
```

**How to Format:**

1. Select the cell(s)
2. Right-click → Format Cells
3. Choose category (Currency, Percentage, etc.)

#### B. Cell Appearance

- **Font**: Arial, Calibri (professional fonts)
- **Size**: 11-12 pt for data, 14-16 pt for headers
- **Colors**: Use sparingly - black text on white background is most readable
- **Borders**: Help separate sections

#### C. Alignment

- **Headers**: Center or Bold
- **Numbers**: Right-align (natural for reading numbers)
- **Text**: Left-align (natural for reading text)

**Practice Exercise:**
Create this simple business table:

```
     A           B         C
1  Product    Price    Quantity
2  Laptop     $999       5
3  Mouse      $25       20
4  Keyboard   $75       10
```

Apply formatting:

1. Bold the headers (Row 1)
2. Format Column B as Currency
3. Add borders around the table

---

# Part B: Business Functions - Your Professional Toolkit

## 1. VLOOKUP - Finding Information Instantly

### What is VLOOKUP and Why is it Essential?

**Real-World Context:**
Imagine you're working in HR and you have a database of 500 employees. Your boss asks you to find John Smith's salary. Without VLOOKUP, you'd have to scroll through 500 rows manually. With VLOOKUP, you can find it instantly.

### VLOOKUP Breakdown

**VLOOKUP stands for:**

- **V** = Vertical (searches down columns)
- **LOOKUP** = Find something

**The VLOOKUP Formula Structure:**

```excel
=VLOOKUP(What you're looking for, Where to look, Which column to return, Exact or approximate match)
```

**Technical Syntax:**

```excel
=VLOOKUP(lookup_value, table_array, col_index_num, [range_lookup])
```

### Step-by-Step VLOOKUP Tutorial

#### Step 1: Create the Employee Database

Create this table starting at A1:

```
     A          B           C          D         E
1  EmpID    First Name   Last Name   Department  Salary
2   101        John       Smith        Sales     $45,000
3   102        Sarah      Johnson      Marketing $52,000
4   103        Mike       Brown        IT        $58,000
5   104        Lisa       Davis        HR        $48,000
6   105        Tom        Wilson       Sales     $46,000
```

#### Step 2: Create the Lookup Section

In cells G1:H5, create:

```
     G              H
1  Search for Employee:
2  Employee ID:      [Input cell]
3  Name:            [VLOOKUP result]
4  Department:      [VLOOKUP result]
5  Salary:          [VLOOKUP result]
```

#### Step 3: Build the VLOOKUP Formulas

**Formula 1: Get Employee Name**

```excel
Cell H3: =VLOOKUP(H2,A2:E6,2,FALSE)&" "&VLOOKUP(H2,A2:E6,3,FALSE)
```

**Let's break this down:**

- `H2`: The cell containing Employee ID we're searching for
- `A2:E6`: The table containing all employee data
- `2`: Return the value from column 2 (First Name)
- `3`: Return the value from column 3 (Last Name)
- `FALSE`: Exact match only
- `&" "&`: Combines first and last name with a space

**Formula 2: Get Department**

```excel
Cell H4: =VLOOKUP(H2,A2:E6,4,FALSE)
```

**Formula 3: Get Salary**

```excel
Cell H5: =VLOOKUP(H2,A2:E6,5,FALSE)
```

#### Step 4: Test the System

1. In cell H2, type: 103
2. Watch the results:
   - H3 shows: "Mike Brown"
   - H4 shows: "IT"
   - H5 shows: "$58,000"

### Common VLOOKUP Errors and Solutions

#### Error #1: #N/A Error

**Cause**: The lookup value doesn't exist in the first column
**Example**: Searching for Employee ID 999 when it doesn't exist

**Solution**: Use IFERROR to handle gracefully

```excel
=IFERROR(VLOOKUP(H2,A2:E6,2,FALSE),"Employee not found")
```

#### Error #2: Wrong Column Number

**Cause**: Using wrong column index number
**Solution**: Count columns carefully starting from 1

#### Error #3: Using TRUE instead of FALSE

**Cause**: TRUE allows approximate matches, which can give wrong results
**Solution**: Always use FALSE for exact matches in business applications

### Practice Exercise: Product Price Lookup

**Create a Product Price List:**

```
     A          B
1  Product    Price
2  Laptop     $999
3  Mouse      $25
4  Keyboard   $75
5  Monitor    $299
6  Printer    $149
```

**Create Order Form:**

```
     D          E         F            G
1  Product   Quantity   Unit Price   Total
2  Laptop      2      [VLOOKUP]    [Formula]
3  Mouse      10      [VLOOKUP]    [Formula]
```

**Your Task:**

1. Write VLOOKUP formulas for Unit Price (column F)
2. Calculate Total (column G) using Quantity × Unit Price
3. Test with different products

---

## 2. IF Statements - Automating Business Decisions

### Understanding IF Statements

**Real-World Context:**
In business, we constantly make decisions based on data. "If sales are above target, give a bonus." "If a customer's credit score is below 600, require a deposit." IF statements automate these decisions.

### IF Statement Structure

**Basic Syntax:**

```excel
=IF(condition, value_if_true, value_if_false)
```

**Think of it as a question:**

- IF this condition is true, do this
- OTHERWISE, do that

### Step-by-Step IF Statement Examples

#### Example 1: Student Grade Classification

Create this table:

```
     A        B        C
1  Student   Score   Grade
2  John       85     [IF Formula]
3  Sarah      92     [IF Formula]
4  Mike       67     [IF Formula]
5  Lisa       78     [IF Formula]
```

**Formula for cell C2:**

```excel
=IF(B2>=80,"A",IF(B2>=70,"B",IF(B2>=60,"C","F")))
```

**Breaking down this nested IF:**

1. IF score ≥ 80, then "A"
2. OTHERWISE, IF score ≥ 70, then "B"
3. OTHERWISE, IF score ≥ 60, then "C"
4. OTHERWISE, "F"

#### Example 2: Sales Commission Calculator

**Business Scenario**: Sales reps get 5% commission on sales over $10,000, otherwise 3%

Create this table:

```
     A           B         C          D
1  Sales Rep   Sales   Commission%  Commission$
2  John       $8,000      [IF]       [Formula]
3  Sarah     $15,000      [IF]       [Formula]
4  Mike      $12,000      [IF]       [Formula]
```

**Formulas:**

```excel
C2: =IF(B2>10000,5%,3%)
D2: =B2*C2
```

#### Example 3: Employee Bonus Eligibility

**Business Rule**: Employees with 2+ years service get a $1,000 bonus

Create this table:

```
     A          B              C
1  Employee   Years Service   Bonus
2  John          3           [IF Formula]
3  Sarah         1           [IF Formula]
4  Mike          5           [IF Formula]
```

**Formula:**

```excel
C2: =IF(B2>=2,$1000,$0)
```

### Advanced IF with AND/OR

#### Using AND (All conditions must be true)

**Business Rule**: Bonus only if sales > $10,000 AND customer satisfaction > 90%

```excel
=IF(AND(B2>10000,C2>90%),$1000,$0)
```

#### Using OR (Any condition can be true)

**Business Rule**: Overtime pay if worked > 40 hours OR worked weekends

```excel
=IF(OR(B2>40,C2="Yes"),"Overtime","Regular")
```

### Common IF Statement Mistakes

#### Mistake 1: Wrong Comparison Operators

```excel
Wrong: =IF(B2="greater than 100","High","Low")
Right: =IF(B2>100,"High","Low")
```

#### Mistake 2: Forgetting Quotes Around Text

```excel
Wrong: =IF(B2>100,High,Low)
Right: =IF(B2>100,"High","Low")
```

#### Mistake 3: Complex Nested IFs

Build complexity gradually - start with simple IF, then add conditions

### Practice Exercise:

Create an employee status checker:

- IF years of service > 5, then "Senior"
- OTHERWISE, "Junior"

---

## 3. Text & Date Functions - Professional Data Handling

### Text Functions for Business

#### CONCATENATE - Combining Text

**Business Use**: Creating full names, addresses, or product codes

**Basic Syntax:**

```excel
=CONCATENATE(text1, text2, text3, ...)
```

**Modern Alternative:**

```excel
=A1&" "&B1  (using & operator)
```

**Example: Creating Full Names**

```
     A        B           C
1  First    Last     Full Name
2   John    Smith   [Formula]
3   Sarah   Johnson [Formula]
```

**Formula for C2:**

```excel
=A2&" "&B2
```

**Result**: "John Smith"

**Advanced Example: Creating Email Addresses**

```excel
=LOWER(A2&"."&B2&"@company.com")
```

**Result**: "<EMAIL>"

#### Text Case Functions

```excel
=UPPER("hello world")     → "HELLO WORLD"
=LOWER("HELLO WORLD")     → "hello world"
=PROPER("hello world")    → "Hello World"
```

### Date Functions for Business

#### TODAY() Function

**Purpose**: Always shows current date

```excel
=TODAY()                  → Current date
```

#### Calculating Age or Tenure

**Formula to calculate years of service:**

```excel
=YEAR(TODAY())-YEAR(B2)
```

**Example: Employee Tenure Calculator**

```
     A         B          C
1  Employee  Hire Date   Years Service
2   John     1/15/2020   [Formula]
3   Sarah    3/22/2018   [Formula]
```

**Formula for C2:**

```excel
=YEAR(TODAY())-YEAR(B2)
```

#### Date Formatting with TEXT Function

```excel
=TEXT(TODAY(),"mm/dd/yyyy")    → "06/25/2025"
=TEXT(TODAY(),"dddd")          → "Wednesday"
=TEXT(TODAY(),"mmmm")          → "June"
```

**Business Application: Creating Report Headers**

```excel
="Sales Report for "&TEXT(TODAY(),"mmmm yyyy")
```

**Result**: "Sales Report for June 2025"

### Practice Exercise:

Create a simple employee information form that:

1. Combines first and last names
2. Calculates years since hire date
3. Creates a professional email address

---

## 📝 **What You've Learned Today**

### Technical Skills Mastered:

✅ **Excel Foundation** - Understanding spreadsheet evolution and structure
✅ **Basic Formulas** - SUM, AVERAGE, COUNT, MAX, MIN with cell references
✅ **Professional Formatting** - Currency, dates, alignment, and borders
✅ **VLOOKUP Mastery** - Finding data in tables with error handling
✅ **IF Statement Logic** - Automating business decisions with conditions
✅ **Text Functions** - Combining and formatting text professionally
✅ **Date Calculations** - Computing tenure and formatting dates

### Business Applications:

✅ **Employee Database Lookups** - Find staff information instantly
✅ **Sales Commission Calculations** - Automate payment calculations
✅ **Performance Categorization** - Grade and classify data automatically
✅ **Professional Email Creation** - Generate contact information
✅ **Report Header Generation** - Create dynamic, dated reports

### Key Takeaways:

✅ **Every formula starts with =** - This tells Excel to calculate
✅ **VLOOKUP finds information** instantly from large datasets
✅ **IF statements automate decisions** based on your business rules
✅ **Error handling prevents crashes** - Always use IFERROR with VLOOKUP
✅ **Professional formatting matters** - Makes data easier to read and understand

---

## 🎯 **Prepare for Class 2**

In your next class, you'll use everything learned today to build a complete Employee Information System that includes:

- **Advanced lookup functionality** with error handling
- **Automatic business calculations** for bonuses and seniority
- **Professional dashboard design** ready for real business use
- **Data validation** to prevent user errors

**What to Bring:**

- This handout for reference
- Your practice files from today
- Questions about any concepts you want to review

**Practice Suggestion:**
Create your own simple lookup table using friends, family, or fictional data. Practice VLOOKUP and IF statements until they feel natural!

---

## 🔧 **Troubleshooting Quick Reference**

### Common Issues and Solutions:

| Problem                            | Likely Cause               | Solution                      |
| ---------------------------------- | -------------------------- | ----------------------------- |
| **#N/A Error in VLOOKUP**    | Lookup value not found     | Use IFERROR or check spelling |
| **Wrong VLOOKUP Result**     | Incorrect column index     | Count columns from 1          |
| **Formula Shows as Text**    | Missing = sign             | Start formula with =          |
| **#VALUE! Error**            | Text in math formula       | Check data types              |
| **IF Statement Not Working** | Missing quotes around text | Use "quotes" for text values  |
| **Date Showing as Number**   | Wrong formatting           | Format cell as Date           |

### Getting Help:

- Use **F1** for Excel help
- **Google your error message** - very effective!
- **Ask classmates** - collaboration is encouraged
- **Check your cell references** - most errors are here

Remember: Making mistakes is part of learning Excel. Every expert has seen these errors before!
