# Data Cleaning in Excel Workshop
## Professional Data Management for Business Applications

---

## Learning Objectives
By the end of this workshop, you will be able to:
- Identify common data quality issues in business datasets
- Apply Excel functions to clean and standardize data
- Create professional, analysis-ready datasets
- Implement data validation to prevent future issues

---

## Essential Data Cleaning Functions

| Function | Purpose | Example | Result |
|----------|---------|---------|---------|
| **TRIM** | Remove extra spaces | `=TRIM(" <PERSON> ")` | "<PERSON>" |
| **PROPER** | Standardize capitalization | `=PROPER("john <PERSON>IT<PERSON>")` | "<PERSON>" |
| **SUBSTITUTE** | Replace specific text | `=SUBSTITUTE("98-123-4567","-","")` | "9812345678" |
| **VALUE** | Convert text to number | `=VALUE("1000")` | 1000 |
| **LEFT/RIGHT/MID** | Extract text portions | `=LEFT("<PERSON>",4)` | "John" |
| **CONCATENATE** | Combine text | `=A1&" "&B1` | Combines cells with space |

---

## Common Data Problems and Solutions

| Problem | Example | Impact | Solution |
|---------|---------|---------|----------|
| **Extra Spaces** | " John Smith " | Lookup failures | `=TRIM(A1)` |
| **Wrong Case** | "john SMITH" | Inconsistent sorting | `=PROPER(A1)` |
| **Text Numbers** | "1000" (as text) | Cannot calculate | `=VALUE(A1)` |
| **Mixed Formats** | Phone: 98-123 vs 98.123 | System errors | `=SUBSTITUTE()` |
| **Missing Data** | Empty cells | Incomplete reports | `=IF(A1="","Missing",A1)` |

---

## Part 1: Understanding Data Quality Issues

### What is Data Cleaning?
Data cleaning is the process of identifying and correcting errors, inconsistencies, and inaccuracies in datasets to ensure data quality and reliability for analysis and decision-making.

### Common Data Quality Problems

#### 1. Text Data Issues
- **Extra spaces**: " John Smith " (spaces before/after text)
- **Inconsistent capitalization**: "john SMITH", "John smith"
- **Special characters**: Smart quotes, tabs, line breaks
- **Abbreviation variations**: "IT" vs "Information Technology"

#### 2. Numerical Data Issues
- **Text numbers**: "1000" stored as text instead of number
- **Currency symbols**: "Rs.50,000" cannot be used in calculations
- **Inconsistent formatting**: "1,000" vs "1000"

#### 3. Date and Time Issues
- **Multiple formats**: "15/01/2024" vs "01/15/2024"
- **Text dates**: "Jan 15, 2024" vs actual date values
- **Invalid dates**: "32/15/2024" (impossible date)

#### 4. Contact Information Issues
- **Phone formats**: "98-123-4567" vs "(98) 123-4567" vs "9812345678"
- **Email problems**: Missing "@" or domain extensions
- **Address inconsistencies**: Different abbreviations and formats

### Data Cleaning Process

#### Step 1: Assess Your Data
- Identify data types (text, numbers, dates)
- Check for missing values
- Look for inconsistent formats
- Find duplicate records

#### Step 2: Plan Your Cleaning Strategy
- Decide on standard formats
- Choose appropriate Excel functions
- Create helper columns for testing
- Document your cleaning rules

#### Step 3: Clean and Validate
- Apply cleaning formulas
- Test on sample data first
- Verify results manually
- Create final clean dataset

---

## Part 2: Hands-On Practice

### Exercise: Clean Employee Database

#### The Challenge
You have received an employee database with multiple data quality issues. Your task is to clean this data for use in a company report.

#### Sample Data
Create this dataset starting at cell A1:

| CustomerID | FirstName | LastName | Email | Phone | Department | Salary | Status |
|------------|-----------|----------|-------|-------|------------|---------|---------|
| EMP001 | ram | SHARMA | <EMAIL> | 98-1234567 | sales | Rs.45,000 | active |
| EMP002 |  sita | gurung | SITA.GURUNG@EMAIL | 98.234.5678 | MARKETING | 52000 | Active |
| EMP003 | Bikash |  thapa | bikash@company | 9803334444 | I.T. | Rs.58,000 |  ACTIVE |
| EMP004 | maya | Shrestha | <EMAIL> | (98)444-5555 | Human Resources | 48000 | inactive |
| EMP005 |  | Karki | <EMAIL> | 98 555 6666 |  | Rs.46,000 | active |

#### Data Problems to Fix
- ❌ Inconsistent name capitalization
- ❌ Extra spaces in text
- ❌ Incomplete email addresses
- ❌ Multiple phone number formats
- ❌ Inconsistent department names
- ❌ Mixed salary formats
- ❌ Missing data

### Step-by-Step Cleaning Solutions

#### 1. Clean Names (Column J - Clean First Name)

**Problem**: Names have inconsistent capitalization and extra spaces
**Solution**: Use TRIM and PROPER functions

```excel
=IF(B2="","[Missing]",PROPER(TRIM(B2)))
```

**What this does**:
- `TRIM(B2)` removes extra spaces
- `PROPER()` capitalizes first letter of each word
- `IF()` handles empty cells

#### 2. Clean Last Names (Column K)

```excel
=IF(C2="","[Missing]",PROPER(TRIM(C2)))
```

#### 3. Create Full Names (Column L)

```excel
=J2&" "&K2
```

#### 4. Clean Email Addresses (Column M)

**Problem**: Incomplete emails and inconsistent formatting
**Solution**: Check for @ symbol and add missing domain

```excel
=IF(D2="","[No Email]",IF(ISERROR(FIND("@",D2)),"Invalid",IF(ISERROR(FIND(".",D2)),LOWER(TRIM(D2))&".com",LOWER(TRIM(D2)))))
```

**What this does**:
- Checks if email is empty
- Verifies @ symbol exists
- Adds .com if domain extension is missing
- Converts to lowercase

#### 5. Standardize Phone Numbers (Column N)

**Problem**: Multiple phone number formats
**Solution**: Remove formatting characters and create standard format

```excel
=IF(E2="","[No Phone]",
"98-"&MID(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(E2,"(",""),")","")," ",""),"-",""),3,4)&"-"&RIGHT(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(E2,"(",""),")","")," ",""),"-",""),4))
```

**What this does**:
- Removes all formatting characters: ( ) - spaces
- Creates standard format: 98-1234-5678

#### 6. Standardize Departments (Column O)

**Problem**: Inconsistent department names
**Solution**: Create standard department names

```excel
=IF(F2="","[Unknown]",
IF(OR(UPPER(TRIM(F2))="SALES",UPPER(TRIM(F2))="SALE"),"Sales",
IF(OR(UPPER(TRIM(F2))="MARKETING",UPPER(TRIM(F2))="MARKET"),"Marketing",
IF(OR(UPPER(TRIM(F2))="IT",UPPER(TRIM(F2))="I.T."),"Information Technology",
IF(OR(UPPER(TRIM(F2))="HR",UPPER(TRIM(F2))="HUMAN RESOURCES"),"Human Resources",
IF(UPPER(TRIM(F2))="FINANCE","Finance",PROPER(TRIM(F2))))))))
```

**What this does**:
- Handles empty departments
- Standardizes common variations
- Uses PROPER case for unknown departments

#### 7. Clean Salary Data (Column P)

**Problem**: Mixed salary formats (some with Rs., some without)
**Solution**: Remove currency symbols and convert to numbers

```excel
=IF(G2="",0,VALUE(SUBSTITUTE(SUBSTITUTE(G2,"Rs.",""),",","")))
```

**What this does**:
- Handles empty salary fields
- Removes "Rs." currency symbol
- Removes comma separators
- Converts text to number for calculations

#### 8. Standardize Status (Column Q)

**Problem**: Inconsistent capitalization in status field
**Solution**: Use PROPER function for consistent formatting

```excel
=IF(H2="","[Unknown]",PROPER(TRIM(H2)))
```

**What this does**:
- Handles empty status fields
- Removes extra spaces
- Standardizes capitalization (Active, Inactive)

### Final Clean Dataset

#### Create Your Final Table (Starting at Column S)

**Headers**: CustomerID | Full Name | Email | Phone | Department | Salary | Status

**Formulas for Row 2**:
- S2: `=A2` (CustomerID)
- T2: `=L2` (Full Name)
- U2: `=M2` (Clean Email)
- V2: `=N2` (Standard Phone)
- W2: `=O2` (Clean Department)
- X2: `=P2` (Clean Salary)
- Y2: `=Q2` (Clean Status)

**Copy these formulas down for all data rows**

---

## Part 3: Best Practices and Quality Control

### Data Cleaning Best Practices

#### 1. Always Preserve Original Data
- Create cleaning formulas in new columns
- Never overwrite original data immediately
- Keep backup copies of your dataset
- Test formulas on sample data first

#### 2. Systematic Approach
- Clean one data type at a time
- Document your cleaning rules
- Use consistent naming for helper columns
- Verify results manually on random samples

#### 3. Quality Standards

**Text Data**:
- No leading/trailing spaces
- Consistent capitalization
- Standardized abbreviations

**Numeric Data**:
- Store as numbers, not text
- Consistent decimal places
- Remove currency symbols for calculations

**Contact Information**:
- Standard phone number format
- Valid email addresses (with @ and domain)
- Consistent case (lowercase for emails)

#### 4. Validation Techniques

**Check for Missing Data**:
```excel
=IF(A2="","Missing","Complete")
```

**Identify Duplicates**:
```excel
=COUNTIF($A$2:$A$100,A2)
```

**Validate Email Format**:
```excel
=IF(AND(ISNUMBER(FIND("@",A2)),ISNUMBER(FIND(".",A2))),"Valid","Invalid")
```

---

## Summary and Next Steps

### What You've Learned
- Identify common data quality issues
- Apply Excel functions to clean messy data
- Create standardized, analysis-ready datasets
- Implement validation techniques
- Follow professional data cleaning practices

### Key Functions Mastered
- **TRIM**: Remove extra spaces
- **PROPER**: Standardize capitalization
- **SUBSTITUTE**: Replace unwanted characters
- **VALUE**: Convert text to numbers
- **IF**: Handle missing data and errors

### Common Troubleshooting

| Error | Cause | Solution |
|-------|-------|----------|
| #VALUE! | Text can't convert to number | Use IFERROR with VALUE |
| #NAME? | Function misspelled | Check spelling |
| Formula shows as text | Missing = sign | Start with = |

### Practice Exercises
1. Clean a customer contact list
2. Standardize product inventory data
3. Prepare survey responses for analysis
4. Clean financial transaction records

### Advanced Learning
- Power Query for large datasets
- Data validation rules
- Conditional formatting for quality control
- VBA for custom cleaning functions

---

## Quick Reference Card

### Essential Cleaning Formulas

**Clean Names**:
```excel
=PROPER(TRIM(A1))
```

**Fix Emails**:
```excel
=LOWER(TRIM(A1))
```

**Standardize Phone**:
```excel
=SUBSTITUTE(SUBSTITUTE(A1,"(",""),")","")
```

**Convert Currency**:
```excel
=VALUE(SUBSTITUTE(A1,"Rs.",""))
```

**Handle Missing Data**:
```excel
=IF(A1="","[Missing]",A1)
```

**Check for Duplicates**:
```excel
=COUNTIF($A:$A,A1)>1
```

---

*This workshop provides practical skills for professional data management. Practice with your own datasets to build expertise.*
