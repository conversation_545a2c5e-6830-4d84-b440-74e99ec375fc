# Data Cleaning in Excel Workshop
## Professional Data Management for Business Applications

---

## Learning Objectives
By the end of this workshop, you will be able to:
- Identify common data quality issues in business datasets
- Apply Excel functions to clean and standardize data
- Create professional, analysis-ready datasets
- Implement data validation to prevent future issues

---

## Essential Data Cleaning Functions

### Basic Text Functions
| Function | Purpose | Syntax | Example | Result |
|----------|---------|---------|---------|---------|
| **TRIM** | Remove leading/trailing spaces | `=TRIM(text)` | `=TRIM(" <PERSON> ")` | "<PERSON>" |
| **CLEAN** | Remove non-printable characters | `=CLEAN(text)` | `=CLEAN(A1)` | Removes tabs, line breaks |
| **PROPER** | Capitalize first letter of each word | `=PROPER(text)` | `=PROPER("john <PERSON>IT<PERSON>")` | "<PERSON>" |
| **UPPER** | Convert to uppercase | `=UPPER(text)` | `=UPPER("john smith")` | "JOHN SMITH" |
| **LOWER** | Convert to lowercase | `=LOWER(text)` | `=LOWER("JOHN SMITH")` | "john smith" |

### Text Manipulation Functions
| Function | Purpose | Syntax | Example | Result |
|----------|---------|---------|---------|---------|
| **SUBSTITUTE** | Replace specific text | `=SUBSTITUTE(text,old,new)` | `=SUBSTITUTE("98-123-4567","-","")` | "9812345678" |
| **LEFT** | Extract characters from left | `=LEFT(text,num_chars)` | `=LEFT("John Smith",4)` | "John" |
| **RIGHT** | Extract characters from right | `=RIGHT(text,num_chars)` | `=RIGHT("John Smith",5)` | "Smith" |
| **MID** | Extract characters from middle | `=MID(text,start,length)` | `=MID("John Smith",6,5)` | "Smith" |
| **LEN** | Count characters | `=LEN(text)` | `=LEN("John Smith")` | 10 |
| **FIND** | Find position of text (case-sensitive) | `=FIND(find_text,within_text)` | `=FIND("@","<EMAIL>")` | 5 |
| **SEARCH** | Find position of text (not case-sensitive) | `=SEARCH(find_text,within_text)` | `=SEARCH("EMAIL","<EMAIL>")` | 6 |

### Data Conversion Functions
| Function | Purpose | Syntax | Example | Result |
|----------|---------|---------|---------|---------|
| **VALUE** | Convert text to number | `=VALUE(text)` | `=VALUE("1000")` | 1000 |
| **TEXT** | Convert number to formatted text | `=TEXT(number,format)` | `=TEXT(1000,"#,##0")` | "1,000" |
| **DATEVALUE** | Convert text to date | `=DATEVALUE(text)` | `=DATEVALUE("1/15/2024")` | 45306 (Excel date) |

### Logical and Error Handling Functions
| Function | Purpose | Syntax | Example | Result |
|----------|---------|---------|---------|---------|
| **IF** | Conditional logic | `=IF(condition,true_value,false_value)` | `=IF(A1="","Missing",A1)` | Shows "Missing" if A1 is empty |
| **ISERROR** | Check if formula returns error | `=ISERROR(formula)` | `=ISERROR(VALUE(A1))` | TRUE if A1 can't convert to number |
| **IFERROR** | Handle errors gracefully | `=IFERROR(formula,error_value)` | `=IFERROR(VALUE(A1),0)` | Returns 0 if conversion fails |
| **ISBLANK** | Check if cell is empty | `=ISBLANK(cell)` | `=ISBLANK(A1)` | TRUE if A1 is empty |
| **ISNUMBER** | Check if value is number | `=ISNUMBER(value)` | `=ISNUMBER(A1)` | TRUE if A1 contains number |
| **ISTEXT** | Check if value is text | `=ISTEXT(value)` | `=ISTEXT(A1)` | TRUE if A1 contains text |

---

## Common Data Problems and Solutions

| Problem Type | Example | Business Impact | Basic Solution | Advanced Solution |
|--------------|---------|-----------------|----------------|-------------------|
| **Extra Spaces** | " John Smith " | Lookup failures, mail merge errors | `=TRIM(A1)` | `=TRIM(CLEAN(A1))` |
| **Wrong Case** | "john SMITH" | Inconsistent sorting, duplicates | `=PROPER(A1)` | `=PROPER(TRIM(A1))` |
| **Text Numbers** | "1000" (stored as text) | Cannot calculate, charts fail | `=VALUE(A1)` | `=IFERROR(VALUE(A1),0)` |
| **Mixed Phone Formats** | "98-123" vs "(98) 123" vs "98.123" | System import errors | `=SUBSTITUTE(A1,"-","")` | Multiple SUBSTITUTE functions |
| **Missing Data** | Empty cells, "N/A", "NULL" | Incomplete reports, calculation errors | `=IF(A1="","Missing",A1)` | `=IF(OR(A1="",A1="N/A"),"Missing",A1)` |
| **Invalid Emails** | "john.email.com" (missing @) | Communication failures | Manual correction | `=IF(ISERROR(FIND("@",A1)),"Invalid",A1)` |
| **Inconsistent Dates** | "1/15/24" vs "15-Jan-2024" | Timeline analysis errors | `=DATEVALUE(A1)` | Complex parsing formulas |
| **Currency Symbols** | "Rs.1,000" vs "1000" | Mathematical operations fail | `=VALUE(SUBSTITUTE(A1,"Rs.",""))` | Multiple symbol removal |

---

## Part 1: Understanding Data Quality Issues

### What is Data Cleaning?
Data cleaning is the process of identifying and correcting errors, inconsistencies, and inaccuracies in datasets to ensure data quality and reliability for analysis and decision-making.

### Common Data Quality Problems

#### 1. Text Data Issues
- **Extra spaces**: " John Smith " (spaces before/after text)
- **Inconsistent capitalization**: "john SMITH", "John smith"
- **Special characters**: Smart quotes, tabs, line breaks
- **Abbreviation variations**: "IT" vs "Information Technology"

#### 2. Numerical Data Issues
- **Text numbers**: "1000" stored as text instead of number
- **Currency symbols**: "Rs.50,000" cannot be used in calculations
- **Inconsistent formatting**: "1,000" vs "1000"

#### 3. Date and Time Issues
- **Multiple formats**: "15/01/2024" vs "01/15/2024"
- **Text dates**: "Jan 15, 2024" vs actual date values
- **Invalid dates**: "32/15/2024" (impossible date)

#### 4. Contact Information Issues
- **Phone formats**: "98-123-4567" vs "(98) 123-4567" vs "9812345678"
- **Email problems**: Missing "@" or domain extensions
- **Address inconsistencies**: Different abbreviations and formats

### Data Cleaning Process

#### Step 1: Assess Your Data
- Identify data types (text, numbers, dates)
- Check for missing values
- Look for inconsistent formats
- Find duplicate records

#### Step 2: Plan Your Cleaning Strategy
- Decide on standard formats
- Choose appropriate Excel functions
- Create helper columns for testing
- Document your cleaning rules

#### Step 3: Clean and Validate
- Apply cleaning formulas
- Test on sample data first
- Verify results manually
- Create final clean dataset

---

## Part 2: Hands-On Practice

### Exercise: Clean Employee Database

#### The Challenge
You have received an employee database with multiple data quality issues. Your task is to clean this data for use in a company report.

#### Sample Data
Create this dataset starting at cell A1:

| CustomerID | FirstName | LastName | Email | Phone | Department | Salary | Status |
|------------|-----------|----------|-------|-------|------------|---------|---------|
| EMP001 | ram | SHARMA | <EMAIL> | 98-1234567 | sales | Rs.45,000 | active |
| EMP002 |  sita | gurung | SITA.GURUNG@EMAIL | 98.234.5678 | MARKETING | 52000 | Active |
| EMP003 | Bikash |  thapa | bikash@company | 9803334444 | I.T. | Rs.58,000 |  ACTIVE |
| EMP004 | maya | Shrestha | <EMAIL> | (98)444-5555 | Human Resources | 48000 | inactive |
| EMP005 |  | Karki | <EMAIL> | 98 555 6666 |  | Rs.46,000 | active |

#### Data Problems to Fix
- ❌ Inconsistent name capitalization
- ❌ Extra spaces in text
- ❌ Incomplete email addresses
- ❌ Multiple phone number formats
- ❌ Inconsistent department names
- ❌ Mixed salary formats
- ❌ Missing data

### Step-by-Step Cleaning Solutions

#### 1. Clean First Names (Column J)

**Problem**: Names have inconsistent capitalization and extra spaces
**Examples**: " ram", "SITA ", "Bikash"

**Step-by-Step Solution**:

**Basic Formula**:
```excel
=PROPER(TRIM(B2))
```

**Breaking it down**:
1. `TRIM(B2)` - First, remove extra spaces from " ram" → "ram"
2. `PROPER()` - Then capitalize properly: "ram" → "Ram"

**Enhanced Formula with Error Handling**:
```excel
=IF(B2="","[Missing]",PROPER(TRIM(B2)))
```

**Breaking it down**:
1. `B2=""` - Check if cell is empty
2. `IF()` - If empty, show "[Missing]", otherwise clean the name
3. `TRIM(B2)` - Remove extra spaces
4. `PROPER()` - Capitalize first letter of each word

**Advanced Formula for Complex Names**:
```excel
=IF(ISBLANK(B2),"[Missing]",IF(LEN(TRIM(B2))<2,"[Invalid]",PROPER(SUBSTITUTE(TRIM(B2),"  "," "))))
```

**Breaking it down**:
1. `ISBLANK(B2)` - Check if cell is completely empty
2. `LEN(TRIM(B2))<2` - Check if name is too short (less than 2 characters)
3. `SUBSTITUTE(TRIM(B2),"  "," ")` - Replace double spaces with single spaces
4. `PROPER()` - Capitalize properly

#### 2. Clean Last Names (Column K)

**Same logic as first names**:
```excel
=IF(C2="","[Missing]",PROPER(TRIM(C2)))
```

#### 3. Create Full Names (Column L)

**Basic Combination**:
```excel
=J2&" "&K2
```

**Enhanced with Error Handling**:
```excel
=IF(OR(J2="[Missing]",K2="[Missing]"),J2&" "&K2,J2&" "&K2)
```

**Advanced Full Name Creation**:
```excel
=IF(AND(J2<>"[Missing]",K2<>"[Missing]"),J2&" "&K2,IF(J2="[Missing]",K2,J2))
```

**Breaking it down**:
1. `AND(J2<>"[Missing]",K2<>"[Missing]")` - Check if both names exist
2. If both exist: combine with space
3. If first name missing: use only last name
4. If last name missing: use only first name

#### 4. Clean Email Addresses (Column M)

**Problem**: Incomplete emails and inconsistent formatting
**Examples**: "SITA.GURUNG@EMAIL", "bikash@company", missing emails

**Step-by-Step Solutions**:

**Basic Email Validation**:
```excel
=IF(ISERROR(FIND("@",D2)),"Invalid Email","Valid Email")
```

**Breaking it down**:
1. `FIND("@",D2)` - Look for @ symbol in email
2. `ISERROR()` - Check if @ symbol was NOT found
3. `IF()` - If no @, mark as "Invalid", otherwise "Valid"

**Email Cleaning with Domain Completion**:
```excel
=IF(D2="","[No Email]",LOWER(TRIM(D2)))
```

**Breaking it down**:
1. `D2=""` - Check if email field is empty
2. `TRIM(D2)` - Remove extra spaces
3. `LOWER()` - Convert to lowercase for consistency

**Advanced Email Cleaning**:
```excel
=IF(D2="","[No Email]",IF(ISERROR(FIND("@",D2)),"Invalid - Missing @",IF(ISERROR(FIND(".",D2,FIND("@",D2))),LOWER(TRIM(D2))&".com",LOWER(TRIM(D2)))))
```

**Breaking it down step by step**:
1. `D2=""` - Check if email is empty → "[No Email]"
2. `FIND("@",D2)` - Look for @ symbol
3. `ISERROR(FIND("@",D2))` - If no @ found → "Invalid - Missing @"
4. `FIND(".",D2,FIND("@",D2))` - Look for dot AFTER the @ symbol
5. `ISERROR(FIND(".",D2,FIND("@",D2)))` - If no dot after @, add ".com"
6. `LOWER(TRIM(D2))` - Clean and lowercase the email
7. `&".com"` - Add .com extension if missing

**Email Domain Extraction** (Bonus):
```excel
=IF(ISERROR(FIND("@",D2)),"No Domain",RIGHT(D2,LEN(D2)-FIND("@",D2)))
```

**Breaking it down**:
1. `FIND("@",D2)` - Find position of @ symbol
2. `LEN(D2)-FIND("@",D2)` - Calculate characters after @
3. `RIGHT()` - Extract domain part after @

**Email Username Extraction** (Bonus):
```excel
=IF(ISERROR(FIND("@",D2)),"No Username",LEFT(D2,FIND("@",D2)-1))
```

**Breaking it down**:
1. `FIND("@",D2)-1` - Find @ position minus 1
2. `LEFT()` - Extract everything before @

#### 5. Standardize Phone Numbers (Column N)

**Problem**: Multiple phone number formats
**Examples**: "98-1234567", "98.234.5678", "(98)444-5555", "98 555 6666"

**Step-by-Step Solutions**:

**Step 1: Remove All Formatting Characters**
```excel
=SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(E2,"(",""),")","")," ",""),"-","")
```

**Breaking it down**:
1. `SUBSTITUTE(E2,"(","")` - Remove opening parenthesis: "(98)123" → "98)123"
2. `SUBSTITUTE(...,")","")` - Remove closing parenthesis: "98)123" → "98123"
3. `SUBSTITUTE(...," ","")` - Remove spaces: "98 123" → "98123"
4. `SUBSTITUTE(...,"-","")` - Remove dashes: "98-123" → "98123"

**Step 2: Extract Numbers Only (Helper Column)**
```excel
=SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(E2,"(",""),")","")," ",""),"-",""),".","")
```

**Additional step**: Remove dots as well

**Step 3: Validate Phone Number Length**
```excel
=IF(LEN(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(E2,"(",""),")","")," ",""),"-",""))=10,"Valid Length","Invalid Length")
```

**Breaking it down**:
1. Clean the phone number (remove formatting)
2. `LEN()` - Count remaining characters
3. Check if exactly 10 digits

**Step 4: Create Standard Format (98-1234-5678)**
```excel
=IF(E2="","[No Phone]","98-"&MID(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(E2,"(",""),")","")," ",""),"-",""),3,4)&"-"&RIGHT(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(E2,"(",""),")","")," ",""),"-",""),4))
```

**Breaking it down**:
1. `E2=""` - Check if phone field is empty
2. Clean phone number (remove all formatting)
3. `MID(...,3,4)` - Extract middle 4 digits (positions 3-6)
4. `RIGHT(...,4)` - Extract last 4 digits
5. Combine as "98-XXXX-XXXX"

**Advanced Phone Cleaning with Validation**:
```excel
=IF(E2="","[No Phone]",IF(LEN(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(E2,"(",""),")","")," ",""),"-",""))<>10,"Invalid Phone","98-"&MID(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(E2,"(",""),")","")," ",""),"-",""),3,4)&"-"&RIGHT(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(E2,"(",""),")","")," ",""),"-",""),4)))
```

**Breaking it down**:
1. Check if empty → "[No Phone]"
2. Clean phone number and check length
3. If not 10 digits → "Invalid Phone"
4. If 10 digits → Format as "98-XXXX-XXXX"

**Phone Type Detection** (Bonus):
```excel
=IF(LEFT(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(E2,"(",""),")","")," ",""),2)="98","Mobile",IF(LEFT(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(E2,"(",""),")","")," ",""),2)="01","Landline","Unknown"))
```

**Breaking it down**:
1. Clean phone number
2. `LEFT(...,2)` - Get first 2 digits
3. Check if starts with "98" (mobile) or "01" (landline)

#### 6. Standardize Departments (Column O)

**Problem**: Inconsistent department names
**Examples**: "sales", "MARKETING", "I.T.", "Human Resources", empty cells

**Step-by-Step Solutions**:

**Basic Department Cleaning**:
```excel
=PROPER(TRIM(F2))
```

**Breaking it down**:
1. `TRIM(F2)` - Remove extra spaces
2. `PROPER()` - Capitalize first letter of each word

**Department Standardization with Common Variations**:
```excel
=IF(F2="","[Unknown]",IF(UPPER(TRIM(F2))="SALES","Sales",IF(UPPER(TRIM(F2))="MARKETING","Marketing",PROPER(TRIM(F2)))))
```

**Breaking it down**:
1. `F2=""` - Check if department is empty
2. `UPPER(TRIM(F2))` - Clean and convert to uppercase for comparison
3. Compare with standard variations
4. Return standardized name or use PROPER case

**Advanced Department Standardization**:
```excel
=IF(F2="","[Unknown]",
IF(OR(UPPER(TRIM(F2))="SALES",UPPER(TRIM(F2))="SALE"),"Sales",
IF(OR(UPPER(TRIM(F2))="MARKETING",UPPER(TRIM(F2))="MARKET"),"Marketing",
IF(OR(UPPER(TRIM(F2))="IT",UPPER(TRIM(F2))="I.T.",UPPER(TRIM(F2))="INFORMATION TECHNOLOGY"),"Information Technology",
IF(OR(UPPER(TRIM(F2))="HR",UPPER(TRIM(F2))="HUMAN RESOURCES"),"Human Resources",
IF(UPPER(TRIM(F2))="FINANCE","Finance",PROPER(TRIM(F2))))))))
```

**Breaking it down step by step**:
1. `F2=""` - Check if empty → "[Unknown]"
2. `OR(UPPER(TRIM(F2))="SALES",UPPER(TRIM(F2))="SALE")` - Check for Sales variations
3. `OR(UPPER(TRIM(F2))="MARKETING",UPPER(TRIM(F2))="MARKET")` - Check for Marketing variations
4. `OR(...="IT",...="I.T.",...="INFORMATION TECHNOLOGY")` - Check for IT variations
5. `OR(...="HR",...="HUMAN RESOURCES")` - Check for HR variations
6. `UPPER(TRIM(F2))="FINANCE"` - Check for Finance
7. `PROPER(TRIM(F2))` - Default: clean and capitalize unknown departments

**Department Lookup Method** (Alternative Approach):
First, create a lookup table in columns AA:AB:
```
AA1: Original    AB1: Standard
AA2: SALES       AB2: Sales
AA3: SALE        AB3: Sales
AA4: MARKETING   AB4: Marketing
AA5: MARKET      AB5: Marketing
AA6: IT          AB6: Information Technology
AA7: I.T.        AB7: Information Technology
```

Then use VLOOKUP:
```excel
=IF(F2="","[Unknown]",IFERROR(VLOOKUP(UPPER(TRIM(F2)),$AA$2:$AB$10,2,FALSE),PROPER(TRIM(F2))))
```

**Breaking it down**:
1. `F2=""` - Check if empty
2. `UPPER(TRIM(F2))` - Clean and uppercase for lookup
3. `VLOOKUP(...,$AA$2:$AB$10,2,FALSE)` - Look up standard name
4. `IFERROR(...,PROPER(TRIM(F2)))` - If not found, use proper case
5. `FALSE` - Exact match only

**Fuzzy Department Matching** (Bonus):
```excel
=IF(ISNUMBER(SEARCH("SALE",UPPER(F2))),"Sales",
IF(ISNUMBER(SEARCH("MARKET",UPPER(F2))),"Marketing",
IF(ISNUMBER(SEARCH("TECH",UPPER(F2))),"Information Technology",
IF(ISNUMBER(SEARCH("HR",UPPER(F2))),"Human Resources",
IF(ISNUMBER(SEARCH("FINANC",UPPER(F2))),"Finance",PROPER(TRIM(F2)))))))
```

**Breaking it down**:
1. `SEARCH("SALE",UPPER(F2))` - Look for "SALE" anywhere in the text
2. `ISNUMBER(SEARCH(...))` - Check if "SALE" was found
3. Continue checking for other department keywords
4. Use partial matching for flexibility

#### 7. Clean Salary Data (Column P)

**Problem**: Mixed salary formats
**Examples**: "Rs.45,000", "52000", "Rs.58,000", empty cells

**Step-by-Step Solutions**:

**Basic Currency Removal**:
```excel
=VALUE(SUBSTITUTE(G2,"Rs.",""))
```

**Breaking it down**:
1. `SUBSTITUTE(G2,"Rs.","")` - Remove "Rs." symbol: "Rs.45,000" → "45,000"
2. `VALUE()` - Convert text to number: "45,000" → 45000

**Handle Commas in Numbers**:
```excel
=VALUE(SUBSTITUTE(SUBSTITUTE(G2,"Rs.",""),",",""))
```

**Breaking it down**:
1. `SUBSTITUTE(G2,"Rs.","")` - Remove currency: "Rs.45,000" → "45,000"
2. `SUBSTITUTE(...,",","")` - Remove commas: "45,000" → "45000"
3. `VALUE()` - Convert to number: "45000" → 45000

**Complete Salary Cleaning with Error Handling**:
```excel
=IF(G2="",0,IFERROR(VALUE(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(G2,"Rs.",""),"Rs",""),",","")),0))
```

**Breaking it down**:
1. `G2=""` - Check if salary is empty → return 0
2. `SUBSTITUTE(G2,"Rs.","")` - Remove "Rs." with period
3. `SUBSTITUTE(...,"Rs","")` - Remove "Rs" without period
4. `SUBSTITUTE(...,",","")` - Remove comma separators
5. `VALUE()` - Convert cleaned text to number
6. `IFERROR(...,0)` - If conversion fails, return 0

**Advanced Salary Cleaning with Multiple Currencies**:
```excel
=IF(G2="",0,IFERROR(VALUE(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(UPPER(G2),"RS.",""),"RS",""),"NPR",""),",","")," ","")),0))
```

**Breaking it down**:
1. `UPPER(G2)` - Convert to uppercase for consistent processing
2. Remove various currency formats: "RS.", "RS", "NPR"
3. Remove commas and spaces
4. Convert to number with error handling

**Salary Range Categorization** (Bonus):
```excel
=IF(P2=0,"No Salary",IF(P2<30000,"Entry Level",IF(P2<60000,"Mid Level",IF(P2<100000,"Senior Level","Executive Level"))))
```

**Breaking it down**:
1. `P2=0` - Check if no salary data
2. `P2<30000` - Entry level (below 30,000)
3. `P2<60000` - Mid level (30,000-59,999)
4. `P2<100000` - Senior level (60,000-99,999)
5. Above 100,000 - Executive level

**Salary Validation** (Bonus):
```excel
=IF(AND(P2>10000,P2<500000),"Valid Salary","Check Salary")
```

**Breaking it down**:
1. `P2>10000` - Minimum reasonable salary
2. `P2<500000` - Maximum reasonable salary
3. `AND()` - Both conditions must be true
4. Flag salaries outside reasonable range

#### 8. Standardize Status (Column Q)

**Problem**: Inconsistent capitalization in status field
**Examples**: "active", "ACTIVE", " ACTIVE", "inactive"

**Step-by-Step Solutions**:

**Basic Status Cleaning**:
```excel
=PROPER(TRIM(H2))
```

**Breaking it down**:
1. `TRIM(H2)` - Remove extra spaces: " ACTIVE" → "ACTIVE"
2. `PROPER()` - Capitalize properly: "ACTIVE" → "Active"

**Status Cleaning with Missing Data Handling**:
```excel
=IF(H2="","[Unknown]",PROPER(TRIM(H2)))
```

**Breaking it down**:
1. `H2=""` - Check if status is empty
2. If empty → "[Unknown]"
3. If not empty → clean and capitalize

**Advanced Status Standardization**:
```excel
=IF(H2="","[Unknown]",IF(OR(UPPER(TRIM(H2))="ACTIVE",UPPER(TRIM(H2))="A"),"Active",IF(OR(UPPER(TRIM(H2))="INACTIVE",UPPER(TRIM(H2))="I"),"Inactive",PROPER(TRIM(H2)))))
```

**Breaking it down**:
1. `H2=""` - Check if empty → "[Unknown]"
2. `UPPER(TRIM(H2))` - Clean and convert to uppercase for comparison
3. `OR(...="ACTIVE",...="A")` - Check for active variations
4. `OR(...="INACTIVE",...="I")` - Check for inactive variations
5. `PROPER(TRIM(H2))` - Default: clean unknown statuses

**Status Validation** (Bonus):
```excel
=IF(OR(UPPER(TRIM(H2))="ACTIVE",UPPER(TRIM(H2))="INACTIVE"),"Valid Status","Invalid Status")
```

**Breaking it down**:
1. Clean and uppercase the status
2. Check if it matches valid options
3. Flag invalid statuses for review

### Advanced Data Cleaning Techniques

#### 9. Data Validation and Quality Scoring

**Completeness Score** (Column R):
```excel
=ROUND((IF(J2<>"[Missing]",1,0)+IF(M2<>"[No Email]",1,0)+IF(N2<>"[No Phone]",1,0)+IF(O2<>"[Unknown]",1,0)+IF(P2>0,1,0))/5*100,0)&"%"
```

**Breaking it down**:
1. `IF(J2<>"[Missing]",1,0)` - Score 1 if name exists, 0 if missing
2. `IF(M2<>"[No Email]",1,0)` - Score 1 if email exists
3. `IF(N2<>"[No Phone]",1,0)` - Score 1 if phone exists
4. `IF(O2<>"[Unknown]",1,0)` - Score 1 if department known
5. `IF(P2>0,1,0)` - Score 1 if salary exists
6. Sum all scores, divide by 5, multiply by 100 for percentage
7. `ROUND(...,0)` - Round to whole number
8. `&"%"` - Add percentage symbol

**Duplicate Detection** (Column S):
```excel
=IF(COUNTIFS($A$2:$A$100,A2,$J$2:$J$100,J2,$K$2:$K$100,K2)>1,"Duplicate","Unique")
```

**Breaking it down**:
1. `COUNTIFS()` - Count rows that match multiple criteria
2. `$A$2:$A$100,A2` - Match CustomerID
3. `$J$2:$J$100,J2` - Match First Name
4. `$K$2:$K$100,K2` - Match Last Name
5. If count > 1, mark as duplicate

#### 10. Advanced Text Analysis

**Name Length Analysis** (Column T):
```excel
=IF(J2="[Missing]","No Name",IF(LEN(J2&K2)>20,"Long Name",IF(LEN(J2&K2)<5,"Short Name","Normal Name")))
```

**Email Domain Analysis** (Column U):
```excel
=IF(M2="[No Email]","No Email",IF(ISNUMBER(SEARCH("gmail",M2)),"Personal",IF(ISNUMBER(SEARCH("yahoo",M2)),"Personal",IF(ISNUMBER(SEARCH("hotmail",M2)),"Personal","Business"))))
```

**Breaking it down**:
1. Check if email exists
2. `SEARCH("gmail",M2)` - Look for "gmail" in email
3. `ISNUMBER(SEARCH(...))` - Check if found
4. Categorize as Personal or Business based on domain

#### 11. Data Transformation Formulas

**Create Initials** (Column V):
```excel
=IF(OR(J2="[Missing]",K2="[Missing]"),"N/A",LEFT(J2,1)&"."&LEFT(K2,1)&".")
```

**Breaking it down**:
1. Check if either name is missing
2. `LEFT(J2,1)` - Get first letter of first name
3. `LEFT(K2,1)` - Get first letter of last name
4. Combine with periods: "J.S."

**Extract Area Code from Phone** (Column W):
```excel
=IF(N2="[No Phone]","No Phone",LEFT(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(N2,"(",""),")","")," ",""),"-",""),2))
```

**Breaking it down**:
1. Clean phone number (remove formatting)
2. `LEFT(...,2)` - Get first 2 digits (area code)

**Calculate Years of Service** (Column X):
```excel
=IF(ISDATE(G2),DATEDIF(G2,TODAY(),"Y")&" years","Invalid Date")
```

**Breaking it down**:
1. `ISDATE(G2)` - Check if hire date is valid
2. `DATEDIF(G2,TODAY(),"Y")` - Calculate years between hire date and today
3. `&" years"` - Add text label

### Final Clean Dataset

#### Create Your Final Table (Starting at Column Y)

**Headers**: CustomerID | Full Name | Email | Phone | Department | Salary | Status | Quality Score

**Formulas for Row 2**:
- Y2: `=A2` (CustomerID)
- Z2: `=L2` (Full Name)
- AA2: `=M2` (Clean Email)
- AB2: `=N2` (Standard Phone)
- AC2: `=O2` (Clean Department)
- AD2: `=P2` (Clean Salary)
- AE2: `=Q2` (Clean Status)
- AF2: `=R2` (Quality Score)

**Copy these formulas down for all data rows**

---

## Part 3: Advanced Data Cleaning and Quality Control

### Complex Data Cleaning Scenarios

#### 1. Handling Multiple Data Issues in One Formula

**Multi-Problem Name Cleaning**:
```excel
=IF(ISBLANK(B2),"[Missing]",IF(LEN(TRIM(B2))<2,"[Too Short]",IF(ISNUMBER(B2),"[Invalid - Number]",PROPER(SUBSTITUTE(SUBSTITUTE(TRIM(B2),"  "," "),"'","'")))))
```

**Breaking it down**:
1. `ISBLANK(B2)` - Check if completely empty
2. `LEN(TRIM(B2))<2` - Check if too short after trimming
3. `ISNUMBER(B2)` - Check if it's a number (invalid for names)
4. `SUBSTITUTE(TRIM(B2),"  "," ")` - Replace double spaces with single
5. `SUBSTITUTE(...,"'","'")` - Fix apostrophes in names like O'Connor
6. `PROPER()` - Capitalize properly

#### 2. Advanced Email Validation

**Comprehensive Email Validation**:
```excel
=IF(D2="","[No Email]",IF(LEN(D2)<5,"[Too Short]",IF(ISERROR(FIND("@",D2)),"[Missing @]",IF(FIND("@",D2)=1,"[@ at Start]",IF(FIND("@",D2)=LEN(D2),"[@ at End]",IF(ISERROR(FIND(".",D2,FIND("@",D2))),"[No Domain]",IF(FIND(".",D2,FIND("@",D2))=LEN(D2),"[Ends with .]",LOWER(TRIM(D2)))))))))
```

**Breaking it down**:
1. Check if empty
2. Check minimum length (5 characters)
3. Check for @ symbol
4. Check @ is not at start
5. Check @ is not at end
6. Check for domain (. after @)
7. Check doesn't end with .
8. If all valid, clean and return

#### 3. Statistical Data Quality Analysis

**Outlier Detection for Salary**:
```excel
=IF(P2=0,"No Salary",IF(ABS(P2-AVERAGE($P$2:$P$100))>2*STDEV($P$2:$P$100),"Outlier","Normal"))
```

**Breaking it down**:
1. `AVERAGE($P$2:$P$100)` - Calculate average salary
2. `STDEV($P$2:$P$100)` - Calculate standard deviation
3. `ABS(P2-AVERAGE(...))` - Distance from average
4. `>2*STDEV(...)` - More than 2 standard deviations away
5. Flag as outlier if outside normal range

**Data Distribution Analysis**:
```excel
=IF(P2=0,"No Data",IF(P2<=PERCENTILE($P$2:$P$100,0.25),"Bottom 25%",IF(P2<=PERCENTILE($P$2:$P$100,0.5),"Below Average",IF(P2<=PERCENTILE($P$2:$P$100,0.75),"Above Average","Top 25%"))))
```

**Breaking it down**:
1. `PERCENTILE($P$2:$P$100,0.25)` - 25th percentile (Q1)
2. `PERCENTILE($P$2:$P$100,0.5)` - 50th percentile (median)
3. `PERCENTILE($P$2:$P$100,0.75)` - 75th percentile (Q3)
4. Categorize each value based on quartiles

#### 4. Advanced Pattern Recognition

**Phone Number Validation with Pattern Checking**:
```excel
=IF(E2="","[No Phone]",IF(LEN(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(E2,"(",""),")","")," ",""),"-",""))<>10,"[Wrong Length]",IF(NOT(ISNUMBER(VALUE(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(E2,"(",""),")","")," ",""),"-","")))),"[Contains Letters]",IF(OR(LEFT(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(E2,"(",""),")","")," ",""),"-",""),2)="98",LEFT(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(E2,"(",""),")","")," ",""),"-",""),2)="01"),"Valid Pattern","[Invalid Pattern]"))))
```

**Breaking it down**:
1. Check if empty
2. Clean phone and check length = 10
3. Check if all characters are numbers
4. Check if starts with valid prefixes (98 or 01)
5. Return validation result

#### 5. Data Consistency Checks

**Cross-Field Validation**:
```excel
=IF(AND(O2="Information Technology",NOT(OR(ISNUMBER(SEARCH("tech",LOWER(M2))),ISNUMBER(SEARCH("it",LOWER(M2)))))),"Email-Dept Mismatch","Consistent")
```

**Breaking it down**:
1. Check if department is IT
2. Check if email contains tech-related terms
3. Flag mismatches between department and email domain

**Salary-Department Consistency**:
```excel
=IF(AND(O2="Information Technology",P2<40000),"Low IT Salary",IF(AND(O2="Sales",P2>80000),"High Sales Salary","Normal Range"))
```

**Breaking it down**:
1. Check department-salary combinations
2. Flag unusual salary ranges for specific departments
3. Help identify data entry errors

### Data Quality Metrics

#### Overall Data Quality Score
```excel
=ROUND(AVERAGE(IF(J2:J100<>"[Missing]",1,0),IF(M2:M100<>"[No Email]",1,0),IF(N2:N100<>"[No Phone]",1,0),IF(O2:O100<>"[Unknown]",1,0),IF(P2:P100>0,1,0))*100,1)&"%"
```

#### Error Rate Calculation
```excel
=ROUND(COUNTIFS(R2:R100,"*Outlier*")/COUNTA(R2:R100)*100,1)&"% Error Rate"
```

#### Completeness by Field
```excel
=COUNTIF(J2:J100,"<>[Missing]")/COUNTA(J2:J100)*100&"% Complete"
```

---

## Part 4: Professional Data Cleaning Workflows

### Best Practices for Large Datasets

#### 1. Systematic Data Assessment

**Data Profiling Formula Set**:

**Count Unique Values**:
```excel
=SUMPRODUCT(1/COUNTIF(A2:A1000,A2:A1000))
```

**Find Most Common Value**:
```excel
=INDEX(A2:A1000,MODE(MATCH(A2:A1000,A2:A1000,0)))
```

**Calculate Null Percentage**:
```excel
=COUNTBLANK(A2:A1000)/ROWS(A2:A1000)*100&"%"
```

**Identify Data Types**:
```excel
=IF(ISNUMBER(A2),"Number",IF(ISDATE(A2),"Date",IF(ISTEXT(A2),"Text","Other")))
```

#### 2. Automated Data Cleaning Pipeline

**Master Cleaning Formula** (combines multiple cleaning steps):
```excel
=IFERROR(
  IF(ISBLANK(A2),"[MISSING]",
    IF(ISNUMBER(A2),A2,
      IF(ISDATE(A2),A2,
        PROPER(TRIM(CLEAN(SUBSTITUTE(SUBSTITUTE(A2,CHAR(160)," "),CHAR(9)," "))))))),
  "[ERROR]")
```

**Breaking it down**:
1. `IFERROR(...,"[ERROR]")` - Catch any formula errors
2. `ISBLANK(A2)` - Handle empty cells
3. `ISNUMBER(A2)` - Preserve numbers as-is
4. `ISDATE(A2)` - Preserve dates as-is
5. `CHAR(160)` - Remove non-breaking spaces
6. `CHAR(9)` - Remove tab characters
7. `CLEAN()` - Remove non-printable characters
8. `TRIM()` - Remove extra spaces
9. `PROPER()` - Standardize capitalization

#### 3. Advanced Error Detection

**Comprehensive Data Validation**:
```excel
=IF(ISBLANK(A2),"Empty",
  IF(AND(ISNUMBER(A2),A2<0),"Negative Number",
    IF(AND(ISTEXT(A2),LEN(A2)>100),"Text Too Long",
      IF(AND(ISTEXT(A2),ISNUMBER(SEARCH("ERROR",UPPER(A2)))),"Contains Error",
        IF(AND(ISTEXT(A2),LEN(TRIM(A2))=0),"Only Spaces","Valid")))))
```

#### 4. Performance Optimization Techniques

**Efficient Multi-Character Removal**:
```excel
=TRIM(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(A1,"(",""),")","")," ",""),"-",""),".",""))
```

**Array-Based Cleaning** (for Excel 365):
```excel
=PROPER(TRIM(A2:A1000))
```

### Industry-Specific Cleaning Examples

#### Financial Data Cleaning

**Currency Standardization**:
```excel
=IFERROR(VALUE(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(UPPER(A2),"$",""),"€",""),"£",""),"RS.",""),"NPR","")),0)
```

**Account Number Validation**:
```excel
=IF(LEN(SUBSTITUTE(A2," ",""))=16,IF(ISNUMBER(VALUE(SUBSTITUTE(A2," ",""))),"Valid Account","Invalid Format"),"Wrong Length")
```

#### Healthcare Data Cleaning

**Patient ID Standardization**:
```excel
=IF(LEN(A2)=10,UPPER(A2),IF(LEN(A2)<10,"P"&RIGHT("*********"&A2,9),LEFT(A2,10)))
```

**Date of Birth Validation**:
```excel
=IF(AND(ISDATE(A2),YEAR(A2)>=1900,YEAR(A2)<=YEAR(TODAY())-1),A2,"Invalid DOB")
```

### Quality Control Dashboard

#### Create Summary Statistics

**Data Quality Summary Table**:

| Metric | Formula | Purpose |
|--------|---------|---------|
| **Total Records** | `=COUNTA(A:A)-1` | Count all data rows |
| **Complete Records** | `=SUMPRODUCT(--(A2:A1000<>""),--(B2:B1000<>""),--(C2:C1000<>""))` | Count fully complete rows |
| **Completeness Rate** | `=Complete_Records/Total_Records*100&"%"` | Percentage of complete data |
| **Duplicate Count** | `=SUMPRODUCT(--(COUNTIFS(A2:A1000,A2:A1000,B2:B1000,B2:B1000)>1))` | Count duplicate records |
| **Error Rate** | `=COUNTIF(Status_Column,"*Error*")/Total_Records*100&"%"` | Percentage of errors |

#### Conditional Formatting for Quality Control

**Highlight Data Issues**:
- Empty cells: `=ISBLANK(A1)`
- Duplicates: `=COUNTIF($A$2:$A$1000,A1)>1`
- Outliers: `=ABS(A1-AVERAGE($A$2:$A$1000))>2*STDEV($A$2:$A$1000)`
- Invalid formats: `=ISERROR(VALUE(A1))`

### Advanced Troubleshooting

#### Common Formula Errors and Solutions

| Error Type | Cause | Solution | Example |
|------------|-------|----------|---------|
| **#VALUE!** | Text in numeric operation | Use IFERROR with VALUE | `=IFERROR(VALUE(A1),0)` |
| **#NAME?** | Function name misspelled | Check spelling | `=PROPER(A1)` not `=PROPPER(A1)` |
| **#REF!** | Invalid cell reference | Check cell references | Update range references |
| **#DIV/0!** | Division by zero | Add zero check | `=IF(B1=0,"",A1/B1)` |
| **#N/A** | VLOOKUP not found | Use IFERROR | `=IFERROR(VLOOKUP(...),""Not Found"")` |
| **Circular Reference** | Formula refers to itself | Change cell reference | Use different cell for result |

#### Performance Issues and Solutions

**Problem**: Formulas are slow with large datasets
**Solution**:
- Use helper columns instead of complex nested formulas
- Replace volatile functions (NOW, TODAY, RAND)
- Use array formulas for bulk operations

**Problem**: Excel crashes with complex formulas
**Solution**:
- Break complex formulas into steps
- Use Power Query for large-scale cleaning
- Consider database solutions for very large datasets

### Next Steps and Advanced Learning

#### Immediate Practice Exercises
1. **Customer Database**: Clean 1000+ customer records with mixed formatting
2. **Sales Data**: Standardize product codes and pricing information
3. **Survey Responses**: Clean free-text responses and categorize answers
4. **Financial Records**: Standardize transaction data from multiple sources

#### Advanced Excel Features
- **Power Query**: Automated data transformation
- **Power Pivot**: Advanced data modeling
- **VBA Macros**: Custom cleaning functions
- **Dynamic Arrays**: Modern Excel formula techniques

#### Professional Development
- **SQL**: Database-level data cleaning
- **Python/R**: Statistical data cleaning
- **Tableau Prep**: Visual data preparation
- **Data Governance**: Enterprise data quality standards

---

## Complete Formula Reference Library

### Text Cleaning Functions
```excel
// Remove all non-alphanumeric characters
=REGEX(A1,"[^A-Za-z0-9 ]","","g")

// Extract numbers only
=VALUE(REGEX(A1,"[^0-9]","","g"))

// Clean and standardize names
=PROPER(TRIM(SUBSTITUTE(SUBSTITUTE(A1,"  "," "),"'","'")))

// Remove special characters
=SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(A1,CHAR(10),""),CHAR(13),""),CHAR(9)," ")
```

### Validation Functions
```excel
// Email validation
=AND(LEN(A1)>5,ISNUMBER(FIND("@",A1)),ISNUMBER(FIND(".",A1,FIND("@",A1))),FIND("@",A1)>1)

// Phone validation (10 digits)
=LEN(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(A1," ",""),"-",""),"(",""))=10

// Date range validation
=AND(ISDATE(A1),A1>=DATE(1900,1,1),A1<=TODAY())

// Numeric range validation
=AND(ISNUMBER(A1),A1>=0,A1<=1000000)
```

### Data Quality Metrics
```excel
// Completeness score
=COUNTA(A2:A1000)/ROWS(A2:A1000)*100

// Accuracy score (valid formats)
=SUMPRODUCT(--(validation_column="Valid"))/COUNTA(validation_column)*100

// Consistency score (standardized values)
=SUMPRODUCT(--(A2:A1000=cleaned_column))/COUNTA(A2:A1000)*100
```

---

*This comprehensive guide provides professional-level data cleaning skills for business and academic applications. Master these techniques through practice with real-world datasets.*
