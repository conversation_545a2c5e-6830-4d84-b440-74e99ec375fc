# Excel Data Cleaning Workshop
## Complete Guide for Business Students

---

## 🎯 Learning Objectives
By completing this workshop, you will learn how to:
- Clean messy text data and make it consistent
- Find and fix errors in data automatically
- Transform raw data into professional format
- Check data quality like a business analyst

---

## 📋 Quick Reference - Essential Functions

| Function | Purpose | Syntax Example |
|----------|---------|----------------|
| `TRIM()` | Remove extra spaces | `=TRIM(A2)` |
| `PROPER()` | Proper case formatting | `=PROPER(B2)` |
| `SUBSTITUTE()` | Replace specific text | `=SUBSTITUTE(C2,"@","AT")` |
| `LEFT()` | Extract leftmost characters | `=LEFT(D2,3)` |
| `RIGHT()` | Extract rightmost characters | `=RIGHT(E2,4)` |
| `MID()` | Extract middle characters | `=MID(F2,2,5)` |
| `FIND()` | Locate text position | `=FIND("@",G2)` |
| `LEN()` | Count characters | `=LEN(H2)` |
| `ISNUMBER()` | Check if value is number | `=ISNUMBER(I2)` |
| `ISBLANK()` | Check if cell is empty | `=ISBLANK(J2)` |
| `CONCATENATE()` | Join text strings | `=CONCATENATE(K2," ",L2)` |
| `TEXT()` | Format numbers as text | `=TEXT(M2,"************")` |

## 🔧 Data Validation Tools

| Tool | Location | Purpose |
|------|----------|---------|
| Remove Duplicates | Data > Remove Duplicates | Delete duplicate rows |
| Text to Columns | Data > Text to Columns | Split delimited data |
| Find & Replace | Ctrl + H | Batch text replacement |
| Conditional Formatting | Home > Conditional Formatting | Highlight data patterns |
| Data Validation | Data > Data Validation | Set input rules |

---

## 📊 Workshop Scenario: Cleaning a Local Business Database

Imagine you work for "Himalayan Electronics", a popular electronics store in Kathmandu. The store has been collecting customer information for years, but the data is very messy. Your manager has asked you to clean this data so the marketing team can send proper promotional messages.

### Common Data Problems We Will Fix:
1. **Names written incorrectly** (some UPPERCASE, some lowercase, extra spaces)
2. **Email addresses missing parts** (no @gmail.com ending)
3. **Phone numbers in different styles** (some 98-1234567, others 9801234567)
4. **Same customer listed twice** with small differences
5. **Missing important information**
6. **Addresses not properly formatted**
7. **Dates written in different ways**

---

## 🏗️ Getting Started

### Step 1: Set Up Your Excel Sheet
1. Open a new Excel workbook
2. In the first row, type these column headers:
   - A1: "Customer_ID"
   - B1: "First_Name"  
   - C1: "Last_Name"
   - D1: "Email"
   - E1: "Phone"
   - F1: "Address"
   - G1: "City"
   - H1: "Registration_Date"
   - I1: "Purchase_Amount"

### Step 2: Copy This Sample Data (Copy and paste starting from Row 2)
```
Customer_ID	First_Name	Last_Name	Email	Phone	Address	City	Registration_Date	Purchase_Amount
HE001	ram	SHARMA	ram.sharmgmail.com	98-1234567	thamel road	kathmandu	01/15/2024	Rs.45,000
HE002	  SITA  	gurung	<EMAIL>	9801234568	lakeside street	pokhara	15-Jan-2024	45000
HE003	Bikash	THAPA	bikash.thapa@	(98) 987-6543	durbar marg	kathmandu	2024/01/15	Rs.65,500
HE001	Ram	Sharma	<EMAIL>	98-12-34567	Thamel Road	Kathmandu	1/15/2024	Rs.45,000
HE004	maya	SHRESTHA	<EMAIL>		new road	lalitpur	Jan-15-2024	75000
HE005	RAJU	karki	raju.karki@hotmail	98-987-6543	bhaktapur square	bhaktapur	01-15-24	Rs.85,200
HE006	kamala	MAHARJAN	<EMAIL>	9898765432	patan dhoka	lalitpur	15/01/2024	55000
HE007	  KIRAN  	basnet	<EMAIL>	98 12 34 567	boudha stupa	kathmandu	2024-15-01	Rs.38,500
HE008	gita	PRADHAN	gita.pradhan@	(98)456-7890	baneshwor road	kathmandu	01/15/2024	
HE009	SURESH	oli	<EMAIL>	98-456-7890	birgunj road	parsa	15-1-2024	Rs.92,300
```

---

## 🧹 Cleaning Tasks (35-45 minutes)

## 🧹 Data Cleaning Steps

### Task 1: Fix Customer Names

**What's Wrong**: Customer names are written in different ways - some all UPPERCASE, some lowercase, and some have extra spaces.

**What We Want**: All names should look professional like "Ram Sharma" (first letter capital, rest small).

**How to Fix It**:
1. **Add helper columns** - Right-click on column D (Email) and select "Insert" twice to add 2 new columns
2. **Label the new columns**:
   - Column D: "Clean_First_Name"
   - Column E: "Clean_Last_Name"

3. **In cell D2, type this formula**:
   ```excel
   =PROPER(TRIM(B2))
   ```
   **What this does**: 
   - `TRIM()` removes extra spaces
   - `PROPER()` makes first letter capital, rest small

4. **In cell E2, type this formula**:
   ```excel
   =PROPER(TRIM(C2))
   ```

5. **Copy the formulas down**:
   - Select cells D2:E2
   - Press Ctrl+C to copy
   - Select range D3:E11 (all remaining rows)
   - Press Ctrl+V to paste

6. **Replace the old names**:
   - Select column D (Clean_First_Name)
   - Press Ctrl+C
   - Click on column B header
   - Right-click > Paste Special > Values
   - Repeat for column E to column C
   - Delete the helper columns D and E

**Result**: Names should now look like "Ram", "Sita", "Bikash" (proper formatting)

### Task 2: Check and Fix Email Addresses

**What's Wrong**: Some emails are incomplete (missing @gmail.com or domain part).

**What We Want**: All emails should have @ symbol and proper domain like @gmail.com.

**How to Fix It**:
1. **Add a helper column** - Insert a column after Email and name it "Email_Status"

2. **In the Email_Status column (let's say F2), type this formula**:
   ```excel
   =IF(AND(FIND("@",D2)>1,FIND(".",D2,FIND("@",D2))>FIND("@",D2)+1,LEN(D2)>5),"Valid","Invalid")
   ```
   **What this does**: Checks if email has @ symbol and a dot after it

3. **Copy this formula down** to all rows

4. **Highlight the bad emails**:
   - Select the Email column (column D)
   - Go to Home > Conditional Formatting > New Rule
   - Choose "Use a formula to determine which cells to format"
   - Type: `=$F2="Invalid"`
   - Choose red background color
   - Click OK

5. **Fix the highlighted emails manually**:
   - Row 2: Change "ram.sharmgmail.com" to "<EMAIL>"
   - Row 4: Change "bikash.thapa@" to "<EMAIL>"  
   - Row 6: Change "raju.karki@hotmail" to "<EMAIL>"
   - Row 9: Change "gita.pradhan@" to "<EMAIL>"

6. **Delete the helper column** when done

**Result**: All emails should show as "Valid" and have proper @ and domain parts

### Task 3: Make Phone Numbers Look the Same

**What's Wrong**: Phone numbers are written in different styles (98-1234567, 9801234568, 98 12 34 567).

**What We Want**: All phone numbers in the same format: "98-1234-5678"

**How to Fix It**:
1. **Add helper column** after Phone column and name it "Clean_Phone"

2. **In the Clean_Phone column, type this simpler approach**:
   First, let's clean each phone number manually to understand the pattern:
   - Select all phone numbers that need fixing
   - Use Find & Replace (Ctrl+H) to remove extra characters:
     - Find: "(" Replace with: (nothing)
     - Find: ")" Replace with: (nothing)  
     - Find: " " Replace with: (nothing)
     - Find: "." Replace with: (nothing)

3. **After cleaning, add dashes in the right places**:
   In your Clean_Phone column, type this formula:
   ```excel
   =LEFT(E2,2) & "-" & MID(E2,3,4) & "-" & RIGHT(E2,4)
   ```
   **What this does**: 
   - Takes first 2 numbers (98)
   - Adds dash (-)
   - Takes next 4 numbers (1234)  
   - Adds dash (-)
   - Takes last 4 numbers (5678)

4. **Copy the formula down** to all rows
5. **Replace original phone numbers** with cleaned ones
6. **Delete helper column**

**Result**: All phones should look like "98-1234-5678"

### Task 4: Remove Duplicate Customers

**What's Wrong**: Customer HE001 appears twice with slightly different information.

**What We Want**: Each customer should appear only once.

**How to Fix It**:

**Method 1: Using Excel's Built-in Tool (Easier)**
1. **Select all your data** (from A1 to your last column and row)
2. **Go to Data menu > Remove Duplicates**
3. **Choose "Customer_ID"** as the column to check for duplicates
4. **Click OK**
5. **Excel will tell you** how many duplicates were removed

**Method 2: Finding Duplicates Manually (To Learn More)**
1. **Add helper column** named "Duplicate_Check"
2. **Type this formula**:
   ```excel
   =COUNTIF($A$2:$A$11,A2)
   ```
   **What this does**: Counts how many times each Customer_ID appears

3. **Any number greater than 1** means that customer appears multiple times
4. **Look at both records** and keep the one with more complete information
5. **Delete the incomplete record**

**Result**: Each Customer_ID should appear only once in your list

### Task 5: Fix Money Amounts and Dates

**What's Wrong**: 
- Money amounts are written differently (Rs.45,000 vs 45000)
- Dates are in different formats (01/15/2024 vs 15-Jan-2024)

**What We Want**: 
- All money amounts as numbers without "Rs." and commas
- All dates in the same format (MM/DD/YYYY)

**Fixing Money Amounts**:
1. **Add helper column** "Clean_Amount"
2. **Type this formula**:
   ```excel
   =VALUE(SUBSTITUTE(SUBSTITUTE(I2,"Rs.",""),",",""))
   ```
   **What this does**: 
   - Removes "Rs." text
   - Removes commas
   - Converts to number

3. **Copy formula down** to all rows
4. **Select the Clean_Amount column** and format as Currency:
   - Right-click > Format Cells > Currency
   - Choose Nepali Rupee if available, or just Number with comma separator

**Fixing Dates**:
1. **Add helper column** "Clean_Date"  
2. **Type this formula**:
   ```excel
   =DATEVALUE(H2)
   ```
   **What this does**: Converts any date format to Excel's standard date

3. **If you get errors**, you may need to fix some dates manually first
4. **Format the Clean_Date column**:
   - Right-click > Format Cells > Date
   - Choose MM/DD/YYYY format

5. **Replace original columns** with cleaned data
6. **Delete helper columns**

**Result**: All amounts as numbers, all dates in MM/DD/YYYY format

### Task 6: Clean Up Addresses and City Names

**What's Wrong**: Addresses and cities are not consistently formatted.

**What We Want**: Proper formatting for addresses and city names.

**How to Fix It**:

**For Addresses**:
1. **Use Find & Replace** (Ctrl+H) to make addresses consistent:
   - Find: "road" → Replace with: "Road"
   - Find: "street" → Replace with: "Street"  
   - Find: "marg" → Replace with: "Marg"
   - Find: "square" → Replace with: "Square"

**For Cities**:
1. **Add helper column** "Clean_City"
2. **Type this formula**:
   ```excel
   =PROPER(G2)
   ```
   **What this does**: Makes first letter of each word capital

3. **Copy formula down** and replace original city column
4. **Delete helper column**

**Result**: Cities should look like "Kathmandu", "Pokhara", "Lalitpur" instead of "kathmandu" or "KATHMANDU"

---

## 🎯 Check Your Work - Data Quality Report

Create a simple report to see how well you cleaned the data:

### Quality Checklist:
Create a small table somewhere in your sheet to track your progress:

| What to Check | How to Check | Goal |
|---------------|--------------|------|
| **All names look professional** | Look at name columns | All should be like "Ram Sharma" |
| **All emails are complete** | Look for @ and .com | Every email should have both |
| **All phones same format** | Look at phone column | All should be "98-1234-5678" |
| **No duplicate customers** | Count Customer_IDs | Each ID appears only once |
| **All dates same format** | Look at date column | All should be MM/DD/YYYY |
| **All amounts are numbers** | Look at amount column | No "Rs." text, just numbers |

### Final Check Steps:
1. **Scroll through your data** and look for anything that seems wrong
2. **Check that every row** has information in all important columns
3. **Make sure Customer_IDs** are all different
4. **Verify phone numbers** all follow the same pattern
5. **Confirm emails** all have @ and a domain

### What Good Data Looks Like:
- [ ] Names: "Ram Sharma" (not "ram SHARMA" or "  Ram  ")
- [ ] Emails: "<EMAIL>" (not "ram.sharmgmail.com")  
- [ ] Phones: "98-1234-5678" (not "98 1234567" or "(98)1234567")
- [ ] Cities: "Kathmandu" (not "kathmandu" or "KATHMANDU")
- [ ] Dates: "01/15/2024" (not "15-Jan-2024" or "2024/01/15")
- [ ] Amounts: "45000" (not "Rs.45,000")

---

## 🏆 Extra Practice (If You Finish Early)

### Challenge 1: Smart Email Checking
Create a formula that tells you if an email uses common providers like Gmail or Yahoo:
```excel
=IF(OR(ISNUMBER(SEARCH("gmail",D2)),ISNUMBER(SEARCH("yahoo",D2)),ISNUMBER(SEARCH("hotmail",D2))),"Common Email","Check This Email")
```

### Challenge 2: Extract Area Information
If some addresses have area names in brackets like "Thamel Road (Tourist Area)", extract just the area:
```excel
=IF(ISNUMBER(FIND("(",F2)),TRIM(MID(F2,FIND("(",F2)+1,FIND(")",F2)-FIND("(",F2)-1)),"No Area Info")
```

### Challenge 3: Create a Quality Score
Give each customer record a score out of 100 based on how complete their information is:
```excel
=((IF(LEN(B2)>0,20,0))+(IF(LEN(C2)>0,20,0))+(IF(ISNUMBER(SEARCH("@",D2)),20,0))+(IF(LEN(E2)>8,20,0))+(IF(ISNUMBER(I2),20,0)))
```
**What this does**: Gives 20 points each for having first name, last name, valid email, proper phone, and purchase amount.

---

## 📝 What You Have Learned

### Skills You Now Have:
✅ **Clean messy names** using PROPER() and TRIM() functions  
✅ **Fix incomplete emails** and check if they're valid  
✅ **Make phone numbers consistent** using text functions  
✅ **Find and remove duplicate records** using Excel tools  
✅ **Convert different date formats** to one standard format  
✅ **Clean currency data** by removing extra text and symbols  
✅ **Apply professional formatting** to addresses and cities  

### Excel Functions You Mastered:
- **TRIM()** - Removes extra spaces
- **PROPER()** - Makes text look professional (First Letter Capital)
- **SUBSTITUTE()** - Replaces unwanted text
- **FIND()** - Locates text within cells
- **IF()** - Makes decisions based on conditions
- **DATEVALUE()** - Converts dates to standard format
- **VALUE()** - Converts text numbers to real numbers

### Why This Matters for Business:
- **Customer Communication**: Clean data means you can send emails and SMS properly
- **Analysis**: Clean data gives accurate business insights
- **Professional Reports**: Clean data makes your reports look professional
- **Time Saving**: Learning these skills saves hours of manual work
- **Accuracy**: Reduces errors in business decisions

### Real-World Applications:
1. **Retail Business**: Clean customer databases for marketing
2. **Banking**: Standardize customer information for loans
3. **Healthcare**: Organize patient records properly
4. **Education**: Clean student information systems
5. **Government**: Organize citizen data for services

---

## 💡 Important Tips for Future Data Cleaning

### Before You Start Cleaning:
1. **Always save a copy** of your original data before making changes
2. **Use helper columns** instead of changing original data directly
3. **Work on small sections first** to test your formulas
4. **Check your work frequently** to catch mistakes early

### Best Practices:
1. **Be consistent** - if you format one phone number as "98-1234-5678", do all the same way
2. **Double-check your formulas** - test them on a few rows before copying to all rows
3. **Keep notes** of what you changed so you can remember later
4. **Save your work often** using Ctrl+S

### When Working with Large Data:
1. **Use Excel's built-in tools** like Remove Duplicates when possible
2. **Learn keyboard shortcuts** like Ctrl+H for Find & Replace
3. **Use filters** to work on specific types of data
4. **Consider Power Query** for very large datasets (advanced feature)

### Common Mistakes to Avoid:
1. **Don't change original data directly** - always use helper columns first
2. **Don't assume all data follows the same pattern** - always check
3. **Don't forget to format your final results** properly
4. **Don't skip the quality check** at the end

---

*Remember: Data cleaning is like organizing your room - it takes time initially, but makes everything easier later. Practice these techniques regularly and you'll become very fast at it!*
